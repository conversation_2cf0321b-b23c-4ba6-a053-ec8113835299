/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* Background Animation */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-rakhi {
    position: absolute;
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
    opacity: 0.7;
}

.rakhi-1 { top: 10%; left: 10%; animation-delay: 0s; }
.rakhi-2 { top: 20%; right: 15%; animation-delay: 1s; }
.rakhi-3 { top: 60%; left: 5%; animation-delay: 2s; }
.rakhi-4 { bottom: 20%; right: 10%; animation-delay: 3s; }
.rakhi-5 { bottom: 40%; left: 20%; animation-delay: 4s; }
.rakhi-6 { top: 40%; right: 5%; animation-delay: 5s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(10deg); }
}

/* Container and Layout */
.container {
    position: relative;
    z-index: 2;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.section {
    display: none;
    width: 100%;
    max-width: 500px;
    animation: fadeInUp 0.8s ease-out;
}

.section.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Card Styles */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Login Card */
.card-header {
    text-align: center;
    margin-bottom: 30px;
}

.rakhi-icon {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

.rakhi-icon i {
    font-size: 3rem;
    color: #ff6b6b;
    animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.rakhi-thread {
    position: absolute;
    top: 50%;
    left: -30px;
    right: -30px;
    height: 3px;
    background: linear-gradient(90deg, #feca57, #ff6b6b, #feca57);
    border-radius: 2px;
    animation: threadGlow 2s ease-in-out infinite alternate;
}

@keyframes threadGlow {
    from { box-shadow: 0 0 5px rgba(255, 107, 107, 0.5); }
    to { box-shadow: 0 0 15px rgba(255, 107, 107, 0.8); }
}

.main-title {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.subtitle {
    color: #666;
    font-size: 1rem;
    font-weight: 300;
}

/* Form Styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.input-group {
    position: relative;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
    font-size: 0.9rem;
}

.input-group input {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.input-group input:focus {
    outline: none;
    border-color: #ff6b6b;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
    transform: translateY(-2px);
}

.input-decoration {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #ff6b6b, #feca57);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.input-group input:focus + .input-decoration {
    transform: scaleX(1);
}

.hint {
    display: block;
    margin-top: 5px;
    font-size: 0.8rem;
    color: #888;
    font-style: italic;
}

/* Ceremony Section */
.ceremony-header {
    text-align: center;
    margin-bottom: 30px;
}

.ceremony-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2.2rem;
    font-weight: 700;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.ceremony-subtitle {
    color: #666;
    font-size: 1rem;
    font-weight: 300;
}

.ceremony-animation {
    text-align: center;
    margin-bottom: 30px;
}

.brother-hand {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

.hand {
    font-size: 4rem;
    margin-bottom: 10px;
    animation: handWave 3s ease-in-out infinite;
}

@keyframes handWave {
    0%, 100% { transform: rotate(-10deg); }
    50% { transform: rotate(10deg); }
}

.wrist-area {
    position: relative;
    width: 100px;
    height: 20px;
    background: linear-gradient(90deg, #f4a261, #e76f51);
    border-radius: 10px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rakhi-being-tied {
    position: relative;
    width: 60px;
    height: 60px;
    opacity: 0;
    animation: rakhiAppear 3s ease-in-out 2s forwards;
}

@keyframes rakhiAppear {
    0% {
        opacity: 0;
        transform: scale(0) rotate(0deg);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2) rotate(180deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(360deg);
    }
}

.ceremony-text {
    margin-top: 20px;
}

.ceremony-text p {
    font-size: 1.1rem;
    color: #555;
    font-style: italic;
    animation: textFadeIn 2s ease-in-out 1s forwards;
    opacity: 0;
}

@keyframes textFadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Button Styles */
.btn-primary, .btn-secondary {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #ff6b6b;
    border: 2px solid #ff6b6b;
}

.btn-secondary:hover {
    background: #ff6b6b;
    color: white;
    transform: translateY(-2px);
}

/* Questions and Quiz Sections */
.section-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    margin-bottom: 10px;
}

.section-subtitle {
    color: #666;
    font-size: 1rem;
    font-weight: 300;
    text-align: center;
    margin-bottom: 30px;
}

.question-container, .quiz-container {
    margin-bottom: 30px;
}

.question, .quiz-question {
    display: none;
    text-align: center;
}

.question.active, .quiz-question.active {
    display: block;
    animation: slideIn 0.5s ease-out;
}

.question h3, .quiz-question h3 {
    color: #ff6b6b;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.question-text {
    font-size: 1.2rem;
    color: #555;
    margin-bottom: 30px;
    font-weight: 400;
}

.options {
    display: grid;
    gap: 15px;
    margin-bottom: 30px;
}

.option-btn {
    padding: 15px 20px;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    color: #555;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.option-btn:hover {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
    transform: translateY(-2px);
}

.option-btn.selected {
    border-color: #ff6b6b;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: white;
    transform: scale(1.02);
}

.option-btn.correct {
    border-color: #28a745;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.option-btn.incorrect {
    border-color: #dc3545;
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    color: white;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 107, 107, 0.2);
    border-radius: 3px;
    margin-bottom: 20px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff6b6b, #feca57);
    border-radius: 3px;
    width: 50%;
    transition: width 0.5s ease;
    animation: progressGlow 2s ease-in-out infinite alternate;
}

.quiz-progress-fill {
    width: 25%;
}

.verify-progress-fill {
    width: 50%;
}

@keyframes progressGlow {
    from { box-shadow: 0 0 5px rgba(255, 107, 107, 0.5); }
    to { box-shadow: 0 0 15px rgba(255, 107, 107, 0.8); }
}

/* Quiz Specific Styles */
.quiz-progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.score-display {
    font-weight: 600;
    color: #ff6b6b;
    font-size: 1.1rem;
}

/* Result Section */
.result-header {
    text-align: center;
    margin-bottom: 30px;
}

.trophy-icon {
    font-size: 4rem;
    color: #feca57;
    margin-bottom: 20px;
    animation: trophyBounce 2s ease-in-out infinite;
}

@keyframes trophyBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.final-score {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ff6b6b;
    margin-bottom: 20px;
}

.result-message {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(254, 202, 87, 0.1));
    padding: 25px;
    border-radius: 15px;
    border-left: 4px solid #ff6b6b;
    margin-bottom: 30px;
    font-size: 1.1rem;
    line-height: 1.6;
    color: #555;
    text-align: center;
}

/* Message Section */
.message-header {
    text-align: center;
    margin-bottom: 30px;
}

.completed-rakhi {
    position: relative;
    display: inline-block;
    width: 100px;
    height: 100px;
    margin-bottom: 20px;
    animation: finalRakhiGlow 3s ease-in-out infinite;
}

@keyframes finalRakhiGlow {
    0%, 100% {
        filter: drop-shadow(0 0 10px rgba(255, 107, 107, 0.5));
        transform: scale(1);
    }
    50% {
        filter: drop-shadow(0 0 20px rgba(255, 107, 107, 0.8));
        transform: scale(1.05);
    }
}

.message-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2.2rem;
    font-weight: 700;
    background: linear-gradient(45deg, #ff6b6b, #feca57, #ff9ff3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.personal-message {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.05), rgba(254, 202, 87, 0.05));
    padding: 30px;
    border-radius: 15px;
    border-left: 4px solid #ff6b6b;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.personal-message::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 15px;
    font-size: 4rem;
    color: rgba(255, 107, 107, 0.1);
    font-family: 'Dancing Script', cursive;
}

.personal-message p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #555;
    margin-bottom: 15px;
}

.personal-message strong {
    color: #ff6b6b;
    font-weight: 600;
}

.wishes-list {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.wishes-list li {
    padding: 10px 0;
    font-size: 1.1rem;
    color: #555;
    border-bottom: 1px solid rgba(255, 107, 107, 0.1);
    animation: wishAppear 0.8s ease-out forwards;
    opacity: 0;
    transform: translateX(-20px);
}

.wishes-list li:nth-child(1) { animation-delay: 0.2s; }
.wishes-list li:nth-child(2) { animation-delay: 0.4s; }
.wishes-list li:nth-child(3) { animation-delay: 0.6s; }
.wishes-list li:nth-child(4) { animation-delay: 0.8s; }
.wishes-list li:nth-child(5) { animation-delay: 1s; }

@keyframes wishAppear {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.signature {
    text-align: center;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 2px solid rgba(255, 107, 107, 0.2);
}

.signature p {
    margin-bottom: 5px;
}

.signature strong {
    font-size: 1.2rem;
    color: #ff6b6b;
}

.message-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Wish Section */
.wish-header {
    text-align: center;
    margin-bottom: 30px;
}

.animated-rakhi {
    position: relative;
    display: inline-block;
    width: 120px;
    height: 120px;
    margin-bottom: 20px;
}

.rakhi-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
    animation: centerPulse 2s ease-in-out infinite;
}

.rakhi-center i {
    color: white;
    font-size: 1.2rem;
}

@keyframes centerPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
}

.rakhi-petals {
    position: relative;
    width: 100%;
    height: 100%;
}

.petal {
    position: absolute;
    width: 25px;
    height: 25px;
    background: linear-gradient(45deg, #feca57, #ff9ff3);
    border-radius: 50% 0;
    top: 50%;
    left: 50%;
    transform-origin: 0 0;
    animation: petalRotate 4s linear infinite;
}

.petal-1 { transform: translate(-50%, -50%) rotate(0deg) translateY(-45px); animation-delay: 0s; }
.petal-2 { transform: translate(-50%, -50%) rotate(60deg) translateY(-45px); animation-delay: 0.2s; }
.petal-3 { transform: translate(-50%, -50%) rotate(120deg) translateY(-45px); animation-delay: 0.4s; }
.petal-4 { transform: translate(-50%, -50%) rotate(180deg) translateY(-45px); animation-delay: 0.6s; }
.petal-5 { transform: translate(-50%, -50%) rotate(240deg) translateY(-45px); animation-delay: 0.8s; }
.petal-6 { transform: translate(-50%, -50%) rotate(300deg) translateY(-45px); animation-delay: 1s; }

@keyframes petalRotate {
    0% { transform: translate(-50%, -50%) rotate(var(--rotation, 0deg)) translateY(-45px) scale(1); }
    50% { transform: translate(-50%, -50%) rotate(var(--rotation, 0deg)) translateY(-45px) scale(1.2); }
    100% { transform: translate(-50%, -50%) rotate(var(--rotation, 0deg)) translateY(-45px) scale(1); }
}

.rakhi-threads {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    z-index: 1;
}

.thread {
    position: absolute;
    top: 0;
    height: 4px;
    background: linear-gradient(90deg, #feca57, #ff6b6b);
    border-radius: 2px;
    animation: threadWave 3s ease-in-out infinite;
}

.thread-left {
    left: -50px;
    right: 50%;
    animation-delay: 0s;
}

.thread-right {
    left: 50%;
    right: -50px;
    animation-delay: 1.5s;
}

@keyframes threadWave {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.wish-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #ff6b6b, #feca57, #ff9ff3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    margin-bottom: 25px;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from { filter: drop-shadow(0 0 5px rgba(255, 107, 107, 0.3)); }
    to { filter: drop-shadow(0 0 15px rgba(255, 107, 107, 0.6)); }
}

.personalized-message {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(254, 202, 87, 0.1));
    padding: 25px;
    border-radius: 15px;
    border-left: 4px solid #ff6b6b;
    margin-bottom: 30px;
    font-size: 1.1rem;
    line-height: 1.6;
    color: #555;
    position: relative;
    overflow: hidden;
}

.personalized-message::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 10px;
    font-size: 4rem;
    color: rgba(255, 107, 107, 0.2);
    font-family: 'Dancing Script', cursive;
}

.personalized-message::after {
    content: '"';
    position: absolute;
    bottom: -30px;
    right: 10px;
    font-size: 4rem;
    color: rgba(255, 107, 107, 0.2);
    font-family: 'Dancing Script', cursive;
}

.wish-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .card {
        padding: 25px;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .wish-title {
        font-size: 2rem;
    }

    .floating-rakhi {
        font-size: 1.5rem;
    }

    .animated-rakhi {
        width: 100px;
        height: 100px;
    }

    .wish-actions {
        flex-direction: column;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .main-title {
        font-size: 2rem;
    }

    .wish-title {
        font-size: 1.8rem;
    }

    .card {
        padding: 20px;
    }

    .personalized-message {
        padding: 20px;
        font-size: 1rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* Additional Visual Enhancements */
.card::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
    border-radius: 22px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover::after {
    opacity: 0.1;
}

/* Enhanced floating elements */
.background-animation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(254, 202, 87, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(72, 219, 251, 0.1) 0%, transparent 50%);
    animation: backgroundShift 10s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(5deg); }
}

/* Special effects for wish section */
.wish-card {
    position: relative;
    overflow: visible;
}

.wish-card::before {
    content: '✨';
    position: absolute;
    top: -20px;
    right: -20px;
    font-size: 2rem;
    animation: sparkle 2s ease-in-out infinite;
}

.wish-card::after {
    content: '🌟';
    position: absolute;
    bottom: -20px;
    left: -20px;
    font-size: 2rem;
    animation: sparkle 2s ease-in-out infinite 1s;
}

@keyframes sparkle {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 1;
    }
}
