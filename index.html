<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happy <PERSON><PERSON><PERSON> My Dear Brother! 🎀</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎀</text></svg>">
    <meta name="description" content="A special <PERSON><PERSON><PERSON> wish filled with love, blessings, and beautiful memories for my beloved brother.">
    <meta name="keywords" content="<PERSON><PERSON><PERSON>, brother, sister love, festival, rakhi, blessings">
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="background-animation">
        <div class="floating-rakhi rakhi-1">🎀</div>
        <div class="floating-rakhi rakhi-2">🌸</div>
        <div class="floating-rakhi rakhi-3">✨</div>
        <div class="floating-rakhi rakhi-4">🎀</div>
        <div class="floating-rakhi rakhi-5">🌸</div>
        <div class="floating-rakhi rakhi-6">✨</div>
    </div>

    <div class="container">
        <!-- Login Section -->
        <section id="login-section" class="section active">
            <div class="card login-card">
                <div class="card-header">
                    <div class="rakhi-icon">
                        <i class="fas fa-heart"></i>
                        <div class="rakhi-thread"></div>
                    </div>
                    <h1 class="main-title">Happy Raksha Bandhan!</h1>
                    <p class="subtitle">A Special Message Awaits You</p>
                </div>

                <form id="login-form" class="login-form">
                    <div class="input-group">
                        <label for="user-name">Your Name</label>
                        <input type="text" id="user-name" name="userName" required>
                        <div class="input-decoration"></div>
                    </div>

                    <div class="input-group">
                        <label for="nickname">Password</label>
                        <input type="password" id="nickname" name="nickname" placeholder="The nickname called by your sibling" required>
                        <div class="input-decoration"></div>
                        <small class="hint">Hint: The nickname your sibling calls you</small>
                    </div>

                    <button type="submit" class="btn-primary">
                        <span>Enter</span>
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </form>
            </div>
        </section>

        <!-- Verification Questions Section -->
        <section id="verification-section" class="section">
            <div class="card verification-card">
                <div class="card-header">
                    <h2 class="section-title">Let's Verify It's Really You!</h2>
                    <p class="section-subtitle">Answer these questions about yourself</p>
                </div>

                <div class="question-container">
                    <div class="question active" id="verify-question-1">
                        <h3>Question 1 of 2</h3>
                        <p class="question-text">What is your favorite thing?</p>
                        <div class="options">
                            <button type="button" class="option-btn" data-value="cars">Cars</button>
                            <button type="button" class="option-btn" data-value="bikes">Bikes</button>
                            <button type="button" class="option-btn" data-value="music">Music</button>
                            <button type="button" class="option-btn" data-value="sports">Sports</button>
                        </div>
                    </div>

                    <div class="question" id="verify-question-2">
                        <h3>Question 2 of 2</h3>
                        <p class="question-text">What is your favorite sweet?</p>
                        <div class="options">
                            <button type="button" class="option-btn" data-value="gulabjamun">Gulab Jamun</button>
                            <button type="button" class="option-btn" data-value="rasmali">Rasmali</button>
                            <button type="button" class="option-btn" data-value="jalebi">Jalebi</button>
                            <button type="button" class="option-btn" data-value="laddu">Laddu</button>
                        </div>
                    </div>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill verify-progress-fill"></div>
                </div>

                <button type="button" id="next-verify-question" class="btn-secondary hidden">
                    Next Question
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </section>

        <!-- Quiz Section -->
        <section id="quiz-section" class="section">
            <div class="card quiz-card">
                <div class="card-header">
                    <h2 class="section-title">Let's See How Much You Know Me!</h2>
                    <p class="section-subtitle">A quiz about your elder sister Supriya</p>
                </div>

                <div class="quiz-container">
                    <div class="quiz-question active" id="quiz-question-1">
                        <h3>Question 1 of 4</h3>
                        <p class="question-text">What is my favorite thing?</p>
                        <div class="options">
                            <button type="button" class="option-btn" data-value="reading">Reading</button>
                            <button type="button" class="option-btn" data-value="sleep">Sleep</button>
                            <button type="button" class="option-btn" data-value="dancing">Dancing</button>
                            <button type="button" class="option-btn" data-value="cooking">Cooking</button>
                        </div>
                    </div>

                    <div class="quiz-question" id="quiz-question-2">
                        <h3>Question 2 of 4</h3>
                        <p class="question-text">What is my favorite thing to eat?</p>
                        <div class="options">
                            <button type="button" class="option-btn" data-value="pizza">Pizza</button>
                            <button type="button" class="option-btn" data-value="chocolates">Chocolates</button>
                            <button type="button" class="option-btn" data-value="icecream">Ice Cream</button>
                            <button type="button" class="option-btn" data-value="sweets">Sweets</button>
                        </div>
                    </div>

                    <div class="quiz-question" id="quiz-question-3">
                        <h3>Question 3 of 4</h3>
                        <p class="question-text">Which food do I prefer very less to eat?</p>
                        <div class="options">
                            <button type="button" class="option-btn" data-value="spicy">Spicy Food</button>
                            <button type="button" class="option-btn" data-value="upitrice">Upit or Coloured Rice</button>
                            <button type="button" class="option-btn" data-value="vegetables">Vegetables</button>
                            <button type="button" class="option-btn" data-value="fruits">Fruits</button>
                        </div>
                    </div>

                    <div class="quiz-question" id="quiz-question-4">
                        <h3>Question 4 of 4</h3>
                        <p class="question-text">What is one of my favorite songs?</p>
                        <div class="options">
                            <button type="button" class="option-btn" data-value="bollywood">Bollywood Hits</button>
                            <button type="button" class="option-btn" data-value="maayavi">MAAYAVI</button>
                            <button type="button" class="option-btn" data-value="classical">Classical Music</button>
                            <button type="button" class="option-btn" data-value="pop">Pop Songs</button>
                        </div>
                    </div>
                </div>

                <div class="quiz-progress">
                    <div class="progress-bar">
                        <div class="progress-fill quiz-progress-fill"></div>
                    </div>
                    <div class="score-display">
                        <span>Score: <span id="current-score">0</span>/4</span>
                    </div>
                </div>

                <button type="button" id="next-quiz-question" class="btn-secondary hidden">
                    Next Question
                    <i class="fas fa-chevron-right"></i>
                </button>

                <button type="button" id="show-quiz-result" class="btn-primary hidden">
                    Show Results
                    <i class="fas fa-trophy"></i>
                </button>
            </div>
        </section>

        <!-- Quiz Result Section -->
        <section id="result-section" class="section">
            <div class="card result-card">
                <div class="result-header">
                    <div class="trophy-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h2 id="result-title">Quiz Complete!</h2>
                    <div class="final-score">
                        <span>Your Score: <span id="final-score">0</span>/4</span>
                    </div>
                </div>

                <div class="result-message" id="result-message">
                    <!-- Dynamic result message will be inserted here -->
                </div>

                <button type="button" id="continue-to-message" class="btn-primary">
                    <span>Continue to Special Message</span>
                    <i class="fas fa-heart"></i>
                </button>
            </div>
        </section>

        <!-- Final Message Section -->
        <section id="message-section" class="section">
            <div class="card message-card">
                <div class="message-header">
                    <div class="completed-rakhi">
                        <div class="rakhi-center">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="rakhi-petals">
                            <div class="petal petal-1"></div>
                            <div class="petal petal-2"></div>
                            <div class="petal petal-3"></div>
                            <div class="petal petal-4"></div>
                            <div class="petal petal-5"></div>
                            <div class="petal petal-6"></div>
                        </div>
                        <div class="rakhi-threads">
                            <div class="thread thread-left"></div>
                            <div class="thread thread-right"></div>
                        </div>
                    </div>
                    <h2 class="message-title">A Special Message from Supriya</h2>
                </div>

                <div class="message-content">
                    <div class="personal-message">
                        <p><strong>My Dearest Brother,</strong></p>

                        <p>Happy Raksha Bandhan! 🎀 As I sit here thinking about you on this special day, my heart fills with so much warmth and gratitude for having you as my brother from another mother.</p>

                        <p>You know, life has this beautiful way of bringing the right people into our lives at the right time. What started as a friendship has blossomed into something so much more precious - a bond that feels as strong and pure as any blood relation.</p>

                        <p>Being of the same age, we've grown up together in so many ways. We've shared our dreams, our fears, our silly jokes, and our deepest thoughts. You've been my partner in crime, my voice of reason, and my biggest cheerleader all rolled into one amazing person.</p>

                        <p>I love how we can be completely ourselves around each other - whether we're teasing each other endlessly, sharing our love for chocolates (and your obsession with bikes! 🏍️), or just enjoying comfortable silences. You've shown me what true friendship and brotherhood really mean.</p>

                        <p>On this Raksha Bandhan, I want you to know that even though we don't share the same parents, you're every bit my real brother. This virtual rakhi represents the invisible thread that has always connected our hearts - a bond of choice, love, and mutual respect.</p>

                        <p>May you always stay the wonderful, caring, and amazing person you are. May your passion for bikes take you on incredible adventures, may you always find joy in the little things (like teasing me! 😄), and may life always give you reasons to smile.</p>

                        <p>Thank you for being my brother by choice, my friend by heart, and my family by love. No matter where life takes us, you'll always have a special place in my heart.</p>

                        <p><strong>Happy Raksha Bandhan, my wonderful brother! I'm so blessed to have you in my life! ❤️</strong></p>

                        <div class="signature">
                            <p><em>With all my love, endless hugs, and warmest wishes,</em></p>
                            <p><strong>Your Loving Sister by Heart,<br>Supriya 💕</strong></p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script src="script.js"></script>
</body>
</html>
