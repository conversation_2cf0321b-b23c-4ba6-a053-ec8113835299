// Global variables for the wish experience
let currentStep = 1;
let ceremonyStarted = false;
let musicPlaying = false;

// DOM elements
const welcomeSection = document.getElementById('welcome-section');
const ceremonySection = document.getElementById('ceremony-section');
const messageSection = document.getElementById('message-section');
const startCeremonyBtn = document.getElementById('start-ceremony');
const continueToMessageBtn = document.getElementById('continue-to-message');
const ceremonyMessage = document.getElementById('ceremony-message');
const rakhiAnimation = document.getElementById('rakhi-animation');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    createFloatingElements();
    startWelcomeAnimations();
});

// Event listeners
function initializeEventListeners() {
    // Start ceremony button
    startCeremonyBtn.addEventListener('click', startRakhiCeremony);

    // Continue to message button
    continueToMessageBtn.addEventListener('click', showFinalMessage);

    // Play music button
    document.getElementById('play-music').addEventListener('click', toggleMusic);

    // Send love back button
    document.getElementById('share-love').addEventListener('click', shareLove);
}

// Start welcome animations
function startWelcomeAnimations() {
    // Add some sparkle effects to the welcome section
    setTimeout(() => {
        createSparkleEffect(welcomeSection);
    }, 1000);
}

// Start the rakhi ceremony
function startRakhiCeremony() {
    ceremonyStarted = true;

    // Add loading animation to button
    const originalText = startCeremonyBtn.innerHTML;
    startCeremonyBtn.innerHTML = '<div class="loading"></div> Preparing ceremony...';
    startCeremonyBtn.disabled = true;

    setTimeout(() => {
        transitionToSection(ceremonySection);
        startCeremonyAnimations();
    }, 2000);
}

// Start ceremony animations
function startCeremonyAnimations() {
    const messages = [
        "As I hold this sacred thread in my hands...",
        "I think of all the beautiful memories we've shared...",
        "With love and prayers in my heart...",
        "I tie this rakhi as a symbol of our eternal bond...",
        "May this thread protect you always, my dear brother!"
    ];

    let messageIndex = 0;

    // Show messages one by one
    const messageInterval = setInterval(() => {
        if (messageIndex < messages.length) {
            ceremonyMessage.textContent = messages[messageIndex];
            ceremonyMessage.style.animation = 'textFadeIn 1s ease-in-out';
            messageIndex++;
        } else {
            clearInterval(messageInterval);
            // Show continue button after ceremony
            setTimeout(() => {
                continueToMessageBtn.classList.remove('hidden');
                continueToMessageBtn.style.animation = 'fadeInUp 0.8s ease-out';
            }, 2000);
        }
    }, 3000);

    // Add sparkle effects during ceremony
    createCeremonyEffects();
}

// Show final message
function showFinalMessage() {
    // Add loading animation
    const originalText = continueToMessageBtn.innerHTML;
    continueToMessageBtn.innerHTML = '<div class="loading"></div> Preparing message...';
    continueToMessageBtn.disabled = true;

    setTimeout(() => {
        transitionToSection(messageSection);
        startMessageAnimations();
    }, 1500);
}

// Start message section animations
function startMessageAnimations() {
    // Animate the wishes list items
    const wishItems = document.querySelectorAll('.wishes-list li');
    wishItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.animation = `wishAppear 0.8s ease-out ${index * 0.2}s forwards`;
        }, 1000);
    });

    // Add floating hearts around the message
    setTimeout(() => {
        createHeartRain();
    }, 2000);
}

// Create ceremony effects
function createCeremonyEffects() {
    const ceremonyCard = document.querySelector('.ceremony-card');

    // Create sparkles around the rakhi
    setInterval(() => {
        const sparkle = document.createElement('div');
        sparkle.textContent = '✨';
        sparkle.style.cssText = `
            position: absolute;
            font-size: 1.5rem;
            pointer-events: none;
            animation: sparkleFloat 3s linear forwards;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            z-index: 10;
        `;

        ceremonyCard.appendChild(sparkle);

        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 3000);
    }, 500);
}

// Create sparkle effect
function createSparkleEffect(container) {
    const sparkles = ['✨', '🌟', '💫', '⭐'];

    setInterval(() => {
        const sparkle = document.createElement('div');
        sparkle.textContent = sparkles[Math.floor(Math.random() * sparkles.length)];
        sparkle.style.cssText = `
            position: absolute;
            font-size: 1.2rem;
            pointer-events: none;
            animation: sparkleFloat 4s linear forwards;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            z-index: 5;
        `;

        container.appendChild(sparkle);

        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 4000);
    }, 2000);
}

// Create heart rain effect
function createHeartRain() {
    const hearts = ['💖', '💕', '💗', '💝', '❤️', '🧡', '💛'];
    const messageCard = document.querySelector('.message-card');

    setInterval(() => {
        const heart = document.createElement('div');
        heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.cssText = `
            position: absolute;
            font-size: 1.5rem;
            pointer-events: none;
            animation: heartFall 6s linear forwards;
            left: ${Math.random() * 100}%;
            top: -50px;
            z-index: 5;
        `;

        messageCard.appendChild(heart);

        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 6000);
    }, 800);
}

// Toggle music
function toggleMusic() {
    const musicBtn = document.getElementById('play-music');

    if (!musicPlaying) {
        // Start music (simulate with notification since we can't embed actual audio)
        musicPlaying = true;
        musicBtn.innerHTML = '<i class="fas fa-pause"></i> Pause Music';
        showNotification('🎵 Playing festive Raksha Bandhan music!', 'success');

        // Add music visualization effect
        document.body.classList.add('music-playing');
    } else {
        // Stop music
        musicPlaying = false;
        musicBtn.innerHTML = '<i class="fas fa-music"></i> Play Festive Music';
        showNotification('🎵 Music paused', 'info');

        document.body.classList.remove('music-playing');
    }
}

// Share love back
function shareLove() {
    const loveMessages = [
        "Thank you for this beautiful rakhi! I love you too, dear sister! 💕",
        "Your love means the world to me! Happy Raksha Bandhan! 🎀",
        "I'm so blessed to have you as my sister! ❤️",
        "This is the most beautiful rakhi ever! Thank you! ✨"
    ];

    const randomMessage = loveMessages[Math.floor(Math.random() * loveMessages.length)];

    if (navigator.share) {
        navigator.share({
            title: 'Raksha Bandhan Love',
            text: randomMessage,
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(randomMessage).then(() => {
            showNotification('Love message copied to clipboard! 💕', 'success');
        });
    }
}

// Transition between sections
function transitionToSection(targetSection) {
    const currentSection = document.querySelector('.section.active');

    currentSection.style.animation = 'fadeOut 0.5s ease-out';

    setTimeout(() => {
        currentSection.classList.remove('active');
        targetSection.classList.add('active');
        targetSection.style.animation = 'fadeInUp 0.8s ease-out';
    }, 500);
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background: ${type === 'error' ? '#ff6b6b' : '#48dbfb'};
        color: white;
        border-radius: 8px;
        z-index: 1000;
        animation: slideInRight 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Create additional floating elements
function createFloatingElements() {
    const container = document.querySelector('.background-animation');
    const hearts = ['💖', '💝', '🎁', '🌺', '🌟', '✨'];
    
    setInterval(() => {
        const heart = document.createElement('div');
        heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.cssText = `
            position: absolute;
            font-size: 1.5rem;
            opacity: 0.6;
            pointer-events: none;
            animation: floatUp 4s linear forwards;
            left: ${Math.random() * 100}%;
            bottom: -50px;
        `;
        
        container.appendChild(heart);
        
        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 4000);
    }, 3000);
}

// Add CSS for additional animations
const additionalStyles = `
    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(-20px); }
    }

    @keyframes slideInRight {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); }
        to { transform: translateX(100%); }
    }

    @keyframes sparkleFloat {
        from {
            transform: translateY(0) rotate(0deg) scale(0);
            opacity: 0;
        }
        50% {
            opacity: 1;
            transform: translateY(-20px) rotate(180deg) scale(1);
        }
        to {
            transform: translateY(-40px) rotate(360deg) scale(0);
            opacity: 0;
        }
    }

    @keyframes heartFall {
        from {
            transform: translateY(-50px) rotate(0deg);
            opacity: 0.8;
        }
        to {
            transform: translateY(100vh) rotate(360deg);
            opacity: 0;
        }
    }

    .music-playing {
        animation: musicPulse 2s ease-in-out infinite;
    }

    @keyframes musicPulse {
        0%, 100% { filter: hue-rotate(0deg); }
        50% { filter: hue-rotate(30deg); }
    }

    .blessing {
        margin-top: 20px;
        padding: 15px;
        background: linear-gradient(135deg, rgba(255, 159, 243, 0.1), rgba(72, 219, 251, 0.1));
        border-radius: 10px;
        text-align: center;
        font-style: italic;
        color: #666;
    }
`;

// Inject additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
