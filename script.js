// Global variables for the authentication and quiz system
let currentVerifyQuestion = 1;
let currentQuizQuestion = 1;
let verificationAnswers = [];
let quizScore = 0;
let quizAnswers = [];
let musicPlaying = false;

// Correct answers
const correctVerifyAnswers = ['bikes', 'rasmali'];
const correctQuizAnswers = ['sleep', 'chocolates', 'upitrice', 'maayavi'];

// DOM elements
const loginSection = document.getElementById('login-section');
const verificationSection = document.getElementById('verification-section');
const quizSection = document.getElementById('quiz-section');
const resultSection = document.getElementById('result-section');
const messageSection = document.getElementById('message-section');
const loginForm = document.getElementById('login-form');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    createFloatingElements();
    startWelcomeAnimations();
});

// Event listeners
function initializeEventListeners() {
    // Login form submission
    loginForm.addEventListener('submit', handleLogin);

    // Option buttons for verification and quiz
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('option-btn')) {
            handleOptionSelection(e.target);
        }
    });

    // Next verification question button
    document.getElementById('next-verify-question').addEventListener('click', handleNextVerifyQuestion);

    // Next quiz question button
    document.getElementById('next-quiz-question').addEventListener('click', handleNextQuizQuestion);

    // Show quiz result button
    document.getElementById('show-quiz-result').addEventListener('click', showQuizResult);

    // Continue to message button
    document.getElementById('continue-to-message').addEventListener('click', showFinalMessage);
}

// Start welcome animations
function startWelcomeAnimations() {
    // Add some sparkle effects to the login section
    setTimeout(() => {
        createSparkleEffect(loginSection);
    }, 1000);
}

// Handle login form submission
function handleLogin(e) {
    e.preventDefault();

    const userName = document.getElementById('user-name').value.trim();
    const nickname = document.getElementById('nickname').value.trim();

    if (!userName || !nickname) {
        showNotification('Please fill in both fields', 'error');
        return;
    }

    // Verify credentials (case insensitive for name)
    const isValidName = userName.toLowerCase() === 'harshal';
    const isValidPassword = nickname.toLowerCase() === 'koti';

    // Add loading animation
    const submitBtn = loginForm.querySelector('.btn-primary');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<div class="loading"></div> Verifying...';
    submitBtn.disabled = true;

    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        if (isValidName && isValidPassword) {
            showNotification('Welcome Harshal! 🎉', 'success');
            // Show crackers animation after successful login
            showCrackersAnimation();
            setTimeout(() => {
                transitionToSection(verificationSection);
            }, 3000); // Increased delay to show crackers animation
        } else {
            if (!isValidName) {
                showNotification('Hmm, that name doesn\'t look right! 🤔', 'error');
            } else {
                showNotification('Wrong nickname! Think about what Supriya calls you 😉', 'error');
            }
        }
    }, 1500);
}

// Handle option selection
function handleOptionSelection(optionBtn) {
    const questionContainer = optionBtn.closest('.question, .quiz-question');
    const allOptions = questionContainer.querySelectorAll('.option-btn');

    // Remove selected class from all options
    allOptions.forEach(btn => btn.classList.remove('selected'));

    // Add selected class to clicked option
    optionBtn.classList.add('selected');

    // Determine which section we're in
    if (questionContainer.closest('#verification-section')) {
        handleVerificationAnswer(optionBtn);
    } else if (questionContainer.closest('#quiz-section')) {
        handleQuizAnswer(optionBtn);
    }
}

// Handle verification answers
function handleVerificationAnswer(optionBtn) {
    const questionId = optionBtn.closest('.question').id;
    const questionNumber = questionId === 'verify-question-1' ? 1 : 2;

    verificationAnswers[questionNumber - 1] = optionBtn.dataset.value;

    if (questionNumber === 1) {
        document.getElementById('next-verify-question').classList.remove('hidden');
    } else {
        // Check if verification passed
        setTimeout(() => {
            checkVerification();
        }, 1000);
    }
}

// Check verification results
function checkVerification() {
    const passed = verificationAnswers[0] === correctVerifyAnswers[0] &&
                   verificationAnswers[1] === correctVerifyAnswers[1];

    if (passed) {
        showFullScreenCelebration('Verification Successful! 🎉', 'success');
        setTimeout(() => {
            transitionToSection(quizSection);
        }, 3000); // Increased delay for celebration
    } else {
        showFullScreenCelebration('Hmm, those answers don\'t seem right. Try again! 🤔', 'error');
        // Reset verification after animation
        setTimeout(() => {
            resetVerification();
        }, 2500);
    }
}

// Reset verification
function resetVerification() {
    verificationAnswers = [];
    currentVerifyQuestion = 1;

    document.getElementById('verify-question-1').classList.add('active');
    document.getElementById('verify-question-2').classList.remove('active');
    document.getElementById('next-verify-question').classList.add('hidden');

    document.querySelectorAll('#verification-section .option-btn').forEach(btn => {
        btn.classList.remove('selected');
    });

    document.querySelector('.progress-fill').style.width = '50%';
}

// Handle next verification question
function handleNextVerifyQuestion() {
    currentVerifyQuestion = 2;

    document.getElementById('verify-question-1').classList.remove('active');
    document.getElementById('verify-question-2').classList.add('active');
    document.getElementById('next-verify-question').classList.add('hidden');

    // Update progress bar
    document.querySelector('.progress-fill').style.width = '100%';
}

// Handle quiz answers
function handleQuizAnswer(optionBtn) {
    const questionContainer = optionBtn.closest('.quiz-question');
    const questionNumber = parseInt(questionContainer.id.split('-')[2]);

    quizAnswers[questionNumber - 1] = optionBtn.dataset.value;

    // Check if answer is correct
    const isCorrect = optionBtn.dataset.value === correctQuizAnswers[questionNumber - 1];
    if (isCorrect) {
        quizScore++;
        optionBtn.classList.add('correct');
        showFullScreenCelebration('Correct! 🎉', 'success');
    } else {
        optionBtn.classList.add('incorrect');
        showFullScreenCelebration('Oops! Not quite right 😅', 'error');
    }

    // Update score display
    document.getElementById('current-score').textContent = quizScore;

    // Show next button or finish quiz
    if (questionNumber < 4) {
        setTimeout(() => {
            document.getElementById('next-quiz-question').classList.remove('hidden');
        }, 1500);
    } else {
        setTimeout(() => {
            document.getElementById('show-quiz-result').classList.remove('hidden');
        }, 1500);
    }
}

// Handle next quiz question
function handleNextQuizQuestion() {
    currentQuizQuestion++;

    // Hide current question and show next
    document.getElementById(`quiz-question-${currentQuizQuestion - 1}`).classList.remove('active');
    document.getElementById(`quiz-question-${currentQuizQuestion}`).classList.add('active');

    // Update progress bar
    const progressPercentage = (currentQuizQuestion / 4) * 100;
    document.querySelector('.quiz-progress-fill').style.width = `${progressPercentage}%`;

    // Hide next button
    document.getElementById('next-quiz-question').classList.add('hidden');
}

// Show quiz result
function showQuizResult() {
    const resultTitle = document.getElementById('result-title');
    const finalScore = document.getElementById('final-score');
    const resultMessage = document.getElementById('result-message');

    finalScore.textContent = quizScore;

    let message = '';
    if (quizScore >= 3) {
        resultTitle.textContent = "Excellent! You're fit to be Supriya's Brother! 🏆";
        message = `
            <p><strong>Wow! You scored ${quizScore}/4!</strong></p>
            <p>You really know your sister well! 😊 You're definitely fit to be Supriya's brother.</p>
            <p><strong>Here is a special note from her on this special occasion.</strong></p>
        `;
    } else if (quizScore >= 2) {
        resultTitle.textContent = "Good! You're fit to be Supriya's Brother! 🎉";
        message = `
            <p><strong>Nice! You scored ${quizScore}/4!</strong></p>
            <p>You know your sister pretty well! You're definitely fit to be Supriya's brother.</p>
            <p><strong>Here is a special note from her on this special occasion.</strong></p>
        `;
    } else {
        resultTitle.textContent = "You're still fit to be Supriya's Brother! 💕";
        message = `
            <p><strong>You scored ${quizScore}/4!</strong></p>
            <p>Well, you might need to spend more time with your sister to know her better! 😄 But you're still her beloved brother.</p>
            <p><strong>Here is a special note from her on this special occasion.</strong></p>
        `;
    }

    resultMessage.innerHTML = message;
    transitionToSection(resultSection);
}

// Show final message
function showFinalMessage() {
    const continueBtn = document.getElementById('continue-to-message');
    const originalText = continueBtn.innerHTML;
    continueBtn.innerHTML = '<div class="loading"></div> Loading message...';
    continueBtn.disabled = true;

    setTimeout(() => {
        // Show rockets animation first
        showRocketsAnimation();
        setTimeout(() => {
            transitionToSection(messageSection);
            startMessageAnimations();
        }, 4000); // Wait for rockets animation to complete
    }, 1500);
}

// Start message section animations
function startMessageAnimations() {
    // Animate the wishes list items
    const wishItems = document.querySelectorAll('.wishes-list li');
    wishItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.animation = `wishAppear 0.8s ease-out ${index * 0.2}s forwards`;
        }, 1000);
    });

    // Add floating hearts around the message
    setTimeout(() => {
        createHeartRain();
    }, 2000);
}

// Create ceremony effects
function createCeremonyEffects() {
    const ceremonyCard = document.querySelector('.ceremony-card');

    // Create sparkles around the rakhi
    setInterval(() => {
        const sparkle = document.createElement('div');
        sparkle.textContent = '✨';
        sparkle.style.cssText = `
            position: absolute;
            font-size: 1.5rem;
            pointer-events: none;
            animation: sparkleFloat 3s linear forwards;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            z-index: 10;
        `;

        ceremonyCard.appendChild(sparkle);

        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 3000);
    }, 500);
}

// Create sparkle effect
function createSparkleEffect(container) {
    const sparkles = ['✨', '🌟', '💫', '⭐'];

    setInterval(() => {
        const sparkle = document.createElement('div');
        sparkle.textContent = sparkles[Math.floor(Math.random() * sparkles.length)];
        sparkle.style.cssText = `
            position: absolute;
            font-size: 1.2rem;
            pointer-events: none;
            animation: sparkleFloat 4s linear forwards;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            z-index: 5;
        `;

        container.appendChild(sparkle);

        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 4000);
    }, 2000);
}

// Create heart rain effect
function createHeartRain() {
    const hearts = ['💖', '💕', '💗', '💝', '❤️', '🧡', '💛'];
    const messageCard = document.querySelector('.message-card');

    setInterval(() => {
        const heart = document.createElement('div');
        heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.cssText = `
            position: absolute;
            font-size: 1.5rem;
            pointer-events: none;
            animation: heartFall 6s linear forwards;
            left: ${Math.random() * 100}%;
            top: -50px;
            z-index: 5;
        `;

        messageCard.appendChild(heart);

        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 6000);
    }, 800);
}

// Show crackers animation after successful login
function showCrackersAnimation() {
    const crackersOverlay = document.createElement('div');
    crackersOverlay.className = 'crackers-overlay';
    crackersOverlay.innerHTML = `
        <div class="crackers-container">
            <div class="welcome-text">Welcome Harshal! 🎉</div>
            <div class="crackers-burst">
                <div class="cracker cracker-1">🎆</div>
                <div class="cracker cracker-2">🎇</div>
                <div class="cracker cracker-3">✨</div>
                <div class="cracker cracker-4">🎆</div>
                <div class="cracker cracker-5">🎇</div>
                <div class="cracker cracker-6">✨</div>
                <div class="cracker cracker-7">🎆</div>
                <div class="cracker cracker-8">🎇</div>
            </div>
        </div>
    `;

    document.body.appendChild(crackersOverlay);

    // Remove after animation
    setTimeout(() => {
        if (crackersOverlay.parentNode) {
            crackersOverlay.parentNode.removeChild(crackersOverlay);
        }
    }, 3000);
}

// Show full-screen celebration for quiz answers
function showFullScreenCelebration(message, type) {
    const celebrationOverlay = document.createElement('div');
    celebrationOverlay.className = `celebration-overlay ${type}`;

    const celebrationContent = type === 'success' ? `
        <div class="celebration-container">
            <div class="celebration-text">${message}</div>
            <div class="celebration-effects">
                <div class="confetti confetti-1">🎉</div>
                <div class="confetti confetti-2">🎊</div>
                <div class="confetti confetti-3">✨</div>
                <div class="confetti confetti-4">🌟</div>
                <div class="confetti confetti-5">🎉</div>
                <div class="confetti confetti-6">🎊</div>
                <div class="confetti confetti-7">✨</div>
                <div class="confetti confetti-8">🌟</div>
                <div class="confetti confetti-9">🎉</div>
                <div class="confetti confetti-10">🎊</div>
            </div>
        </div>
    ` : `
        <div class="celebration-container">
            <div class="celebration-text">${message}</div>
            <div class="celebration-effects">
                <div class="sad-emoji sad-1">😅</div>
                <div class="sad-emoji sad-2">🤔</div>
                <div class="sad-emoji sad-3">😊</div>
            </div>
        </div>
    `;

    celebrationOverlay.innerHTML = celebrationContent;
    document.body.appendChild(celebrationOverlay);

    // Remove after animation
    setTimeout(() => {
        if (celebrationOverlay.parentNode) {
            celebrationOverlay.parentNode.removeChild(celebrationOverlay);
        }
    }, 2000);
}

// Show rockets animation before final message
function showRocketsAnimation() {
    const rocketsOverlay = document.createElement('div');
    rocketsOverlay.className = 'rockets-overlay';
    rocketsOverlay.innerHTML = `
        <div class="rockets-container">
            <div class="main-title-animation">Happy Raksha Bandhan!</div>
            <div class="subtitle-animation">A Special Message from Supriya</div>
            <div class="rockets-launch">
                <div class="rocket rocket-1">🚀</div>
                <div class="rocket rocket-2">🚀</div>
                <div class="rocket rocket-3">🚀</div>
                <div class="rocket rocket-4">🚀</div>
                <div class="rocket rocket-5">🚀</div>
            </div>
            <div class="fireworks-display">
                <div class="firework firework-1">🎆</div>
                <div class="firework firework-2">🎇</div>
                <div class="firework firework-3">🎆</div>
                <div class="firework firework-4">🎇</div>
                <div class="firework firework-5">🎆</div>
                <div class="firework firework-6">🎇</div>
                <div class="firework firework-7">🎆</div>
                <div class="firework firework-8">🎇</div>
            </div>
            <div class="sparkles-rain">
                <div class="sparkle sparkle-1">✨</div>
                <div class="sparkle sparkle-2">🌟</div>
                <div class="sparkle sparkle-3">✨</div>
                <div class="sparkle sparkle-4">🌟</div>
                <div class="sparkle sparkle-5">✨</div>
                <div class="sparkle sparkle-6">🌟</div>
                <div class="sparkle sparkle-7">✨</div>
                <div class="sparkle sparkle-8">🌟</div>
            </div>
        </div>
    `;

    document.body.appendChild(rocketsOverlay);

    // Remove after animation
    setTimeout(() => {
        if (rocketsOverlay.parentNode) {
            rocketsOverlay.parentNode.removeChild(rocketsOverlay);
        }
    }, 4000);
}



// Transition between sections
function transitionToSection(targetSection) {
    const currentSection = document.querySelector('.section.active');

    currentSection.style.animation = 'fadeOut 0.5s ease-out';

    setTimeout(() => {
        currentSection.classList.remove('active');
        targetSection.classList.add('active');
        targetSection.style.animation = 'fadeInUp 0.8s ease-out';
    }, 500);
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background: ${type === 'error' ? '#ff6b6b' : '#48dbfb'};
        color: white;
        border-radius: 8px;
        z-index: 1000;
        animation: slideInRight 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Create additional floating elements
function createFloatingElements() {
    const container = document.querySelector('.background-animation');
    const hearts = ['💖', '💝', '🎁', '🌺', '🌟', '✨'];
    
    setInterval(() => {
        const heart = document.createElement('div');
        heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.cssText = `
            position: absolute;
            font-size: 1.5rem;
            opacity: 0.6;
            pointer-events: none;
            animation: floatUp 4s linear forwards;
            left: ${Math.random() * 100}%;
            bottom: -50px;
        `;
        
        container.appendChild(heart);
        
        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 4000);
    }, 3000);
}

// Add CSS for additional animations
const additionalStyles = `
    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(-20px); }
    }

    @keyframes slideInRight {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); }
        to { transform: translateX(100%); }
    }

    @keyframes sparkleFloat {
        from {
            transform: translateY(0) rotate(0deg) scale(0);
            opacity: 0;
        }
        50% {
            opacity: 1;
            transform: translateY(-20px) rotate(180deg) scale(1);
        }
        to {
            transform: translateY(-40px) rotate(360deg) scale(0);
            opacity: 0;
        }
    }

    @keyframes heartFall {
        from {
            transform: translateY(-50px) rotate(0deg);
            opacity: 0.8;
        }
        to {
            transform: translateY(100vh) rotate(360deg);
            opacity: 0;
        }
    }

    /* Crackers Animation Styles */
    .crackers-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: overlayFadeIn 0.5s ease-out;
    }

    .crackers-container {
        text-align: center;
        position: relative;
    }

    .welcome-text {
        font-size: 3rem;
        font-weight: bold;
        color: #fff;
        margin-bottom: 30px;
        animation: textBounce 1s ease-out;
        font-family: 'Dancing Script', cursive;
    }

    .crackers-burst {
        position: relative;
        width: 300px;
        height: 300px;
        margin: 0 auto;
    }

    .cracker {
        position: absolute;
        font-size: 3rem;
        animation: crackerBurst 2s ease-out forwards;
    }

    .cracker-1 { top: 50%; left: 50%; animation-delay: 0.2s; }
    .cracker-2 { top: 20%; left: 80%; animation-delay: 0.4s; }
    .cracker-3 { top: 80%; left: 20%; animation-delay: 0.6s; }
    .cracker-4 { top: 20%; left: 20%; animation-delay: 0.8s; }
    .cracker-5 { top: 80%; left: 80%; animation-delay: 1s; }
    .cracker-6 { top: 50%; left: 10%; animation-delay: 1.2s; }
    .cracker-7 { top: 50%; left: 90%; animation-delay: 1.4s; }
    .cracker-8 { top: 10%; left: 50%; animation-delay: 1.6s; }

    @keyframes overlayFadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes textBounce {
        0% { transform: scale(0); opacity: 0; }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); opacity: 1; }
    }

    @keyframes crackerBurst {
        0% {
            transform: translate(-50%, -50%) scale(0) rotate(0deg);
            opacity: 0;
        }
        50% {
            transform: translate(-50%, -50%) scale(1.5) rotate(180deg);
            opacity: 1;
        }
        100% {
            transform: translate(-50%, -50%) scale(2) rotate(360deg);
            opacity: 0;
        }
    }

    /* Full Screen Celebration Styles */
    .celebration-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: celebrationFadeIn 0.3s ease-out;
    }

    .celebration-overlay.success {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.9), rgba(32, 201, 151, 0.9));
    }

    .celebration-overlay.error {
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.9), rgba(253, 126, 20, 0.9));
    }

    .celebration-container {
        text-align: center;
        position: relative;
    }

    .celebration-text {
        font-size: 2.5rem;
        font-weight: bold;
        color: #fff;
        margin-bottom: 30px;
        animation: celebrationTextPop 0.6s ease-out;
        font-family: 'Dancing Script', cursive;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .celebration-effects {
        position: relative;
        width: 400px;
        height: 200px;
        margin: 0 auto;
    }

    .confetti {
        position: absolute;
        font-size: 2rem;
        animation: confettiFall 1.5s ease-out forwards;
    }

    .confetti-1 { top: 0; left: 10%; animation-delay: 0.1s; }
    .confetti-2 { top: 0; left: 30%; animation-delay: 0.2s; }
    .confetti-3 { top: 0; left: 50%; animation-delay: 0.3s; }
    .confetti-4 { top: 0; left: 70%; animation-delay: 0.4s; }
    .confetti-5 { top: 0; left: 90%; animation-delay: 0.5s; }
    .confetti-6 { top: 50%; left: 20%; animation-delay: 0.6s; }
    .confetti-7 { top: 50%; left: 40%; animation-delay: 0.7s; }
    .confetti-8 { top: 50%; left: 60%; animation-delay: 0.8s; }
    .confetti-9 { top: 50%; left: 80%; animation-delay: 0.9s; }
    .confetti-10 { top: 100%; left: 50%; animation-delay: 1s; }

    .sad-emoji {
        position: absolute;
        font-size: 3rem;
        animation: sadBounce 1s ease-out forwards;
    }

    .sad-1 { top: 30%; left: 30%; animation-delay: 0.2s; }
    .sad-2 { top: 30%; left: 70%; animation-delay: 0.4s; }
    .sad-3 { top: 70%; left: 50%; animation-delay: 0.6s; }

    @keyframes celebrationFadeIn {
        from { opacity: 0; transform: scale(0.8); }
        to { opacity: 1; transform: scale(1); }
    }

    @keyframes celebrationTextPop {
        0% { transform: scale(0); opacity: 0; }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); opacity: 1; }
    }

    @keyframes confettiFall {
        0% {
            transform: translateY(-50px) rotate(0deg) scale(0);
            opacity: 0;
        }
        50% {
            transform: translateY(50px) rotate(180deg) scale(1);
            opacity: 1;
        }
        100% {
            transform: translateY(150px) rotate(360deg) scale(0.5);
            opacity: 0;
        }
    }

    @keyframes sadBounce {
        0% { transform: scale(0) rotate(0deg); opacity: 0; }
        50% { transform: scale(1.2) rotate(10deg); opacity: 1; }
        100% { transform: scale(1) rotate(0deg); opacity: 1; }
    }

    /* Rockets Animation Styles */
    .rockets-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: rocketOverlayFadeIn 0.5s ease-out;
    }

    .rockets-container {
        text-align: center;
        position: relative;
        width: 100%;
        height: 100%;
    }

    .main-title-animation {
        position: absolute;
        top: 20%;
        left: 50%;
        transform: translateX(-50%);
        font-size: 4rem;
        font-weight: bold;
        color: #fff;
        font-family: 'Dancing Script', cursive;
        text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
        animation: titleSlideIn 1s ease-out;
        z-index: 10;
    }

    .subtitle-animation {
        position: absolute;
        top: 35%;
        left: 50%;
        transform: translateX(-50%);
        font-size: 2rem;
        color: #feca57;
        font-family: 'Poppins', sans-serif;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        animation: subtitleSlideIn 1s ease-out 0.5s both;
        z-index: 10;
    }

    .rockets-launch {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .rocket {
        position: absolute;
        font-size: 3rem;
        animation: rocketLaunch 2s ease-out forwards;
    }

    .rocket-1 { bottom: -50px; left: 10%; animation-delay: 1s; }
    .rocket-2 { bottom: -50px; left: 30%; animation-delay: 1.2s; }
    .rocket-3 { bottom: -50px; left: 50%; animation-delay: 1.4s; }
    .rocket-4 { bottom: -50px; left: 70%; animation-delay: 1.6s; }
    .rocket-5 { bottom: -50px; left: 90%; animation-delay: 1.8s; }

    .fireworks-display {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .firework {
        position: absolute;
        font-size: 4rem;
        opacity: 0;
        animation: fireworkExplode 1s ease-out forwards;
    }

    .firework-1 { top: 15%; left: 15%; animation-delay: 2.2s; }
    .firework-2 { top: 25%; left: 85%; animation-delay: 2.4s; }
    .firework-3 { top: 45%; left: 25%; animation-delay: 2.6s; }
    .firework-4 { top: 35%; left: 75%; animation-delay: 2.8s; }
    .firework-5 { top: 55%; left: 50%; animation-delay: 3s; }
    .firework-6 { top: 65%; left: 20%; animation-delay: 3.2s; }
    .firework-7 { top: 75%; left: 80%; animation-delay: 3.4s; }
    .firework-8 { top: 85%; left: 60%; animation-delay: 3.6s; }

    .sparkles-rain {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .sparkle {
        position: absolute;
        font-size: 2rem;
        animation: sparkleRain 3s linear infinite;
    }

    .sparkle-1 { top: -50px; left: 12%; animation-delay: 2.5s; }
    .sparkle-2 { top: -50px; left: 25%; animation-delay: 2.7s; }
    .sparkle-3 { top: -50px; left: 38%; animation-delay: 2.9s; }
    .sparkle-4 { top: -50px; left: 51%; animation-delay: 3.1s; }
    .sparkle-5 { top: -50px; left: 64%; animation-delay: 3.3s; }
    .sparkle-6 { top: -50px; left: 77%; animation-delay: 3.5s; }
    .sparkle-7 { top: -50px; left: 90%; animation-delay: 3.7s; }
    .sparkle-8 { top: -50px; left: 5%; animation-delay: 3.9s; }

    @keyframes rocketOverlayFadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes titleSlideIn {
        0% {
            transform: translateX(-50%) translateY(-100px);
            opacity: 0;
        }
        100% {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }
    }

    @keyframes subtitleSlideIn {
        0% {
            transform: translateX(-50%) translateY(100px);
            opacity: 0;
        }
        100% {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }
    }

    @keyframes rocketLaunch {
        0% {
            transform: translateY(0) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(-100vh) rotate(15deg);
            opacity: 0;
        }
    }

    @keyframes fireworkExplode {
        0% {
            transform: scale(0);
            opacity: 0;
        }
        50% {
            transform: scale(1.5);
            opacity: 1;
        }
        100% {
            transform: scale(2);
            opacity: 0;
        }
    }

    @keyframes sparkleRain {
        0% {
            transform: translateY(-50px) rotate(0deg);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translateY(100vh) rotate(360deg);
            opacity: 0;
        }
    }

    .blessing {
        margin-top: 20px;
        padding: 15px;
        background: linear-gradient(135deg, rgba(255, 159, 243, 0.1), rgba(72, 219, 251, 0.1));
        border-radius: 10px;
        text-align: center;
        font-style: italic;
        color: #666;
    }

    /* Mobile Responsive for Animations */
    @media (max-width: 768px) {
        .welcome-text, .celebration-text {
            font-size: 2rem;
        }

        .main-title-animation {
            font-size: 2.5rem;
        }

        .subtitle-animation {
            font-size: 1.5rem;
        }

        .crackers-burst {
            width: 250px;
            height: 250px;
        }

        .celebration-effects {
            width: 300px;
            height: 150px;
        }

        .cracker, .rocket {
            font-size: 2rem;
        }

        .firework {
            font-size: 2.5rem;
        }

        .sparkle {
            font-size: 1.5rem;
        }

        .confetti, .sad-emoji {
            font-size: 1.5rem;
        }
    }
`;

// Inject additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
