// Global variables for the authentication and quiz system
let currentVerifyQuestion = 1;
let currentQuizQuestion = 1;
let verificationAnswers = [];
let quizScore = 0;
let quizAnswers = [];
let musicPlaying = false;

// Correct answers
const correctVerifyAnswers = ['bikes', 'rasmali'];
const correctQuizAnswers = ['sleep', 'chocolates', 'upitrice', 'maayavi'];

// DOM elements
const loginSection = document.getElementById('login-section');
const verificationSection = document.getElementById('verification-section');
const quizSection = document.getElementById('quiz-section');
const resultSection = document.getElementById('result-section');
const messageSection = document.getElementById('message-section');
const loginForm = document.getElementById('login-form');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    createFloatingElements();
    startWelcomeAnimations();
});

// Event listeners
function initializeEventListeners() {
    // Login form submission
    loginForm.addEventListener('submit', handleLogin);

    // Option buttons for verification and quiz
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('option-btn')) {
            handleOptionSelection(e.target);
        }
    });

    // Next verification question button
    document.getElementById('next-verify-question').addEventListener('click', handleNextVerifyQuestion);

    // Next quiz question button
    document.getElementById('next-quiz-question').addEventListener('click', handleNextQuizQuestion);

    // Show quiz result button
    document.getElementById('show-quiz-result').addEventListener('click', showQuizResult);

    // Continue to message button
    document.getElementById('continue-to-message').addEventListener('click', showFinalMessage);
}

// Start welcome animations
function startWelcomeAnimations() {
    // Add some sparkle effects to the welcome section
    setTimeout(() => {
        createSparkleEffect(welcomeSection);
    }, 1000);
}

// Handle login form submission
function handleLogin(e) {
    e.preventDefault();

    const userName = document.getElementById('user-name').value.trim();
    const nickname = document.getElementById('nickname').value.trim();

    if (!userName || !nickname) {
        showNotification('Please fill in both fields', 'error');
        return;
    }

    // Add loading animation
    const submitBtn = loginForm.querySelector('.btn-primary');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<div class="loading"></div> Verifying...';
    submitBtn.disabled = true;

    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        transitionToSection(verificationSection);
    }, 1500);
}

// Handle option selection
function handleOptionSelection(optionBtn) {
    const questionContainer = optionBtn.closest('.question, .quiz-question');
    const allOptions = questionContainer.querySelectorAll('.option-btn');

    // Remove selected class from all options
    allOptions.forEach(btn => btn.classList.remove('selected'));

    // Add selected class to clicked option
    optionBtn.classList.add('selected');

    // Determine which section we're in
    if (questionContainer.closest('#verification-section')) {
        handleVerificationAnswer(optionBtn);
    } else if (questionContainer.closest('#quiz-section')) {
        handleQuizAnswer(optionBtn);
    }
}

// Handle verification answers
function handleVerificationAnswer(optionBtn) {
    const questionId = optionBtn.closest('.question').id;
    const questionNumber = questionId === 'verify-question-1' ? 1 : 2;

    verificationAnswers[questionNumber - 1] = optionBtn.dataset.value;

    if (questionNumber === 1) {
        document.getElementById('next-verify-question').classList.remove('hidden');
    } else {
        // Check if verification passed
        setTimeout(() => {
            checkVerification();
        }, 1000);
    }
}

// Check verification results
function checkVerification() {
    const passed = verificationAnswers[0] === correctVerifyAnswers[0] &&
                   verificationAnswers[1] === correctVerifyAnswers[1];

    if (passed) {
        showNotification('Verification successful! 🎉', 'success');
        setTimeout(() => {
            transitionToSection(quizSection);
        }, 2000);
    } else {
        showNotification('Hmm, those answers don\'t seem right. Try again! 🤔', 'error');
        // Reset verification
        resetVerification();
    }
}

// Reset verification
function resetVerification() {
    verificationAnswers = [];
    currentVerifyQuestion = 1;

    document.getElementById('verify-question-1').classList.add('active');
    document.getElementById('verify-question-2').classList.remove('active');
    document.getElementById('next-verify-question').classList.add('hidden');

    document.querySelectorAll('#verification-section .option-btn').forEach(btn => {
        btn.classList.remove('selected');
    });

    document.querySelector('.progress-fill').style.width = '50%';
}

// Handle next verification question
function handleNextVerifyQuestion() {
    currentVerifyQuestion = 2;

    document.getElementById('verify-question-1').classList.remove('active');
    document.getElementById('verify-question-2').classList.add('active');
    document.getElementById('next-verify-question').classList.add('hidden');

    // Update progress bar
    document.querySelector('.progress-fill').style.width = '100%';
}

// Handle quiz answers
function handleQuizAnswer(optionBtn) {
    const questionContainer = optionBtn.closest('.quiz-question');
    const questionNumber = parseInt(questionContainer.id.split('-')[2]);

    quizAnswers[questionNumber - 1] = optionBtn.dataset.value;

    // Check if answer is correct
    const isCorrect = optionBtn.dataset.value === correctQuizAnswers[questionNumber - 1];
    if (isCorrect) {
        quizScore++;
        optionBtn.classList.add('correct');
        showNotification('Correct! 🎉', 'success');
    } else {
        optionBtn.classList.add('incorrect');
        showNotification('Oops! Not quite right 😅', 'error');
    }

    // Update score display
    document.getElementById('current-score').textContent = quizScore;

    // Show next button or finish quiz
    if (questionNumber < 4) {
        setTimeout(() => {
            document.getElementById('next-quiz-question').classList.remove('hidden');
        }, 1500);
    } else {
        setTimeout(() => {
            document.getElementById('show-quiz-result').classList.remove('hidden');
        }, 1500);
    }
}

// Handle next quiz question
function handleNextQuizQuestion() {
    currentQuizQuestion++;

    // Hide current question and show next
    document.getElementById(`quiz-question-${currentQuizQuestion - 1}`).classList.remove('active');
    document.getElementById(`quiz-question-${currentQuizQuestion}`).classList.add('active');

    // Update progress bar
    const progressPercentage = (currentQuizQuestion / 4) * 100;
    document.querySelector('.quiz-progress-fill').style.width = `${progressPercentage}%`;

    // Hide next button
    document.getElementById('next-quiz-question').classList.add('hidden');
}

// Show quiz result
function showQuizResult() {
    const resultTitle = document.getElementById('result-title');
    const finalScore = document.getElementById('final-score');
    const resultMessage = document.getElementById('result-message');

    finalScore.textContent = quizScore;

    let message = '';
    if (quizScore >= 3) {
        resultTitle.textContent = "Excellent! You're fit to be Supriya's Brother! 🏆";
        message = `
            <p><strong>Wow! You scored ${quizScore}/4!</strong></p>
            <p>You really know your sister well! 😊 You're definitely fit to be Supriya's brother.</p>
            <p><strong>Here is a special note from her on this special occasion.</strong></p>
        `;
    } else if (quizScore >= 2) {
        resultTitle.textContent = "Good! You're fit to be Supriya's Brother! 🎉";
        message = `
            <p><strong>Nice! You scored ${quizScore}/4!</strong></p>
            <p>You know your sister pretty well! You're definitely fit to be Supriya's brother.</p>
            <p><strong>Here is a special note from her on this special occasion.</strong></p>
        `;
    } else {
        resultTitle.textContent = "You're still fit to be Supriya's Brother! 💕";
        message = `
            <p><strong>You scored ${quizScore}/4!</strong></p>
            <p>Well, you might need to spend more time with your sister to know her better! 😄 But you're still her beloved brother.</p>
            <p><strong>Here is a special note from her on this special occasion.</strong></p>
        `;
    }

    resultMessage.innerHTML = message;
    transitionToSection(resultSection);
}

// Show final message
function showFinalMessage() {
    const continueBtn = document.getElementById('continue-to-message');
    const originalText = continueBtn.innerHTML;
    continueBtn.innerHTML = '<div class="loading"></div> Loading message...';
    continueBtn.disabled = true;

    setTimeout(() => {
        transitionToSection(messageSection);
        startMessageAnimations();
    }, 1500);
}

// Start message section animations
function startMessageAnimations() {
    // Animate the wishes list items
    const wishItems = document.querySelectorAll('.wishes-list li');
    wishItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.animation = `wishAppear 0.8s ease-out ${index * 0.2}s forwards`;
        }, 1000);
    });

    // Add floating hearts around the message
    setTimeout(() => {
        createHeartRain();
    }, 2000);
}

// Create ceremony effects
function createCeremonyEffects() {
    const ceremonyCard = document.querySelector('.ceremony-card');

    // Create sparkles around the rakhi
    setInterval(() => {
        const sparkle = document.createElement('div');
        sparkle.textContent = '✨';
        sparkle.style.cssText = `
            position: absolute;
            font-size: 1.5rem;
            pointer-events: none;
            animation: sparkleFloat 3s linear forwards;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            z-index: 10;
        `;

        ceremonyCard.appendChild(sparkle);

        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 3000);
    }, 500);
}

// Create sparkle effect
function createSparkleEffect(container) {
    const sparkles = ['✨', '🌟', '💫', '⭐'];

    setInterval(() => {
        const sparkle = document.createElement('div');
        sparkle.textContent = sparkles[Math.floor(Math.random() * sparkles.length)];
        sparkle.style.cssText = `
            position: absolute;
            font-size: 1.2rem;
            pointer-events: none;
            animation: sparkleFloat 4s linear forwards;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            z-index: 5;
        `;

        container.appendChild(sparkle);

        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 4000);
    }, 2000);
}

// Create heart rain effect
function createHeartRain() {
    const hearts = ['💖', '💕', '💗', '💝', '❤️', '🧡', '💛'];
    const messageCard = document.querySelector('.message-card');

    setInterval(() => {
        const heart = document.createElement('div');
        heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.cssText = `
            position: absolute;
            font-size: 1.5rem;
            pointer-events: none;
            animation: heartFall 6s linear forwards;
            left: ${Math.random() * 100}%;
            top: -50px;
            z-index: 5;
        `;

        messageCard.appendChild(heart);

        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 6000);
    }, 800);
}



// Transition between sections
function transitionToSection(targetSection) {
    const currentSection = document.querySelector('.section.active');

    currentSection.style.animation = 'fadeOut 0.5s ease-out';

    setTimeout(() => {
        currentSection.classList.remove('active');
        targetSection.classList.add('active');
        targetSection.style.animation = 'fadeInUp 0.8s ease-out';
    }, 500);
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background: ${type === 'error' ? '#ff6b6b' : '#48dbfb'};
        color: white;
        border-radius: 8px;
        z-index: 1000;
        animation: slideInRight 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Create additional floating elements
function createFloatingElements() {
    const container = document.querySelector('.background-animation');
    const hearts = ['💖', '💝', '🎁', '🌺', '🌟', '✨'];
    
    setInterval(() => {
        const heart = document.createElement('div');
        heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.cssText = `
            position: absolute;
            font-size: 1.5rem;
            opacity: 0.6;
            pointer-events: none;
            animation: floatUp 4s linear forwards;
            left: ${Math.random() * 100}%;
            bottom: -50px;
        `;
        
        container.appendChild(heart);
        
        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 4000);
    }, 3000);
}

// Add CSS for additional animations
const additionalStyles = `
    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(-20px); }
    }

    @keyframes slideInRight {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); }
        to { transform: translateX(100%); }
    }

    @keyframes sparkleFloat {
        from {
            transform: translateY(0) rotate(0deg) scale(0);
            opacity: 0;
        }
        50% {
            opacity: 1;
            transform: translateY(-20px) rotate(180deg) scale(1);
        }
        to {
            transform: translateY(-40px) rotate(360deg) scale(0);
            opacity: 0;
        }
    }

    @keyframes heartFall {
        from {
            transform: translateY(-50px) rotate(0deg);
            opacity: 0.8;
        }
        to {
            transform: translateY(100vh) rotate(360deg);
            opacity: 0;
        }
    }

    .music-playing {
        animation: musicPulse 2s ease-in-out infinite;
    }

    @keyframes musicPulse {
        0%, 100% { filter: hue-rotate(0deg); }
        50% { filter: hue-rotate(30deg); }
    }

    .blessing {
        margin-top: 20px;
        padding: 15px;
        background: linear-gradient(135deg, rgba(255, 159, 243, 0.1), rgba(72, 219, 251, 0.1));
        border-radius: 10px;
        text-align: center;
        font-style: italic;
        color: #666;
    }
`;

// Inject additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
