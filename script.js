// Global variables to store user data
let userData = {
    siblingName: '',
    yourName: '',
    answers: []
};

let currentQuestion = 1;

// DOM elements
const loginSection = document.getElementById('login-section');
const questionsSection = document.getElementById('questions-section');
const wishSection = document.getElementById('wish-section');
const loginForm = document.getElementById('login-form');
const nextQuestionBtn = document.getElementById('next-question');
const progressFill = document.querySelector('.progress-fill');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    createFloatingElements();
});

// Event listeners
function initializeEventListeners() {
    // Login form submission
    loginForm.addEventListener('submit', handleLogin);
    
    // Option buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('option-btn')) {
            handleOptionSelection(e.target);
        }
    });
    
    // Next question button
    nextQuestionBtn.addEventListener('click', handleNextQuestion);
    
    // Share wish button
    document.getElementById('share-wish').addEventListener('click', shareWish);
    
    // Create another wish button
    document.getElementById('create-another').addEventListener('click', resetApplication);
}

// Handle login form submission
function handleLogin(e) {
    e.preventDefault();
    
    const siblingName = document.getElementById('sibling-name').value.trim();
    const yourName = document.getElementById('your-name').value.trim();
    
    if (!siblingName || !yourName) {
        showNotification('Please fill in both names', 'error');
        return;
    }
    
    userData.siblingName = siblingName;
    userData.yourName = yourName;
    
    // Add loading animation
    const submitBtn = loginForm.querySelector('.btn-primary');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<div class="loading"></div> Processing...';
    submitBtn.disabled = true;
    
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        transitionToSection(questionsSection);
    }, 1500);
}

// Handle option selection
function handleOptionSelection(optionBtn) {
    const questionContainer = optionBtn.closest('.question');
    const allOptions = questionContainer.querySelectorAll('.option-btn');
    
    // Remove selected class from all options
    allOptions.forEach(btn => btn.classList.remove('selected'));
    
    // Add selected class to clicked option
    optionBtn.classList.add('selected');
    
    // Store the answer
    const questionNumber = questionContainer.id === 'question-1' ? 1 : 2;
    userData.answers[questionNumber - 1] = optionBtn.dataset.value;
    
    // Show next button or proceed to final wish
    if (questionNumber === 1) {
        nextQuestionBtn.classList.remove('hidden');
        nextQuestionBtn.style.display = 'flex';
        nextQuestionBtn.style.animation = 'fadeInUp 0.5s ease-out';
    } else {
        // Both questions answered, proceed to wish
        setTimeout(() => {
            generateFinalWish();
            transitionToSection(wishSection);
        }, 1000);
    }
}

// Handle next question
function handleNextQuestion() {
    currentQuestion = 2;
    
    // Hide current question and show next
    document.getElementById('question-1').classList.remove('active');
    document.getElementById('question-2').classList.add('active');
    
    // Update progress bar
    progressFill.style.width = '100%';
    
    // Hide next button
    nextQuestionBtn.classList.add('hidden');
}

// Generate personalized final wish
function generateFinalWish() {
    const memory = userData.answers[0];
    const wish = userData.answers[1];
    
    const memoryMessages = {
        'childhood-games': `those beautiful childhood days when you both played together, creating memories that still bring smiles`,
        'shared-secrets': `all the secrets you've shared and dreams you've discussed under the starlit sky`,
        'support-times': `how you've always been there for each other through thick and thin, like true companions`,
        'celebrations': `all the festivals you've celebrated together, making each moment more special`
    };
    
    const wishMessages = {
        'happiness': `may your life be filled with endless joy, laughter, and beautiful moments that make your heart dance with happiness`,
        'success': `may success follow you in every step you take, and may all your dreams turn into beautiful realities`,
        'health': `may you always be blessed with good health, prosperity, and the strength to overcome any challenge`,
        'protection': `may divine blessings always protect you, and may you always find peace and safety in life's journey`
    };
    
    const finalMessage = `
        <p><strong>Dear ${userData.siblingName},</strong></p>
        <p>As I tie this sacred thread of Raksha Bandhan, my heart fills with warmth remembering ${memoryMessages[memory]}.</p>
        <p>On this special day, I pray that ${wishMessages[wish]}.</p>
        <p>You are not just my sibling, but my best friend, my confidant, and my partner in all of life's adventures. This rakhi carries with it all my love, prayers, and promises to always be there for you.</p>
        <p>May our bond grow stronger with each passing day, and may we continue to create beautiful memories together.</p>
        <p><strong>With all my love,<br>${userData.yourName}</strong></p>
        <div class="blessing">
            <em>"May the thread of love bind us forever, and may happiness always find its way to you." 🌟</em>
        </div>
    `;
    
    document.getElementById('final-message').innerHTML = finalMessage;
}

// Transition between sections
function transitionToSection(targetSection) {
    const currentSection = document.querySelector('.section.active');
    
    currentSection.style.animation = 'fadeOut 0.5s ease-out';
    
    setTimeout(() => {
        currentSection.classList.remove('active');
        targetSection.classList.add('active');
        targetSection.style.animation = 'fadeInUp 0.8s ease-out';
    }, 500);
}

// Share wish functionality
function shareWish() {
    const wishText = `🎀 Happy Raksha Bandhan! 🎀\n\nA special wish from ${userData.yourName} to ${userData.siblingName}\n\nMay this sacred bond of love continue to grow stronger! ✨\n\n#RakshaBandhan #SiblingLove`;
    
    if (navigator.share) {
        navigator.share({
            title: 'Raksha Bandhan Wish',
            text: wishText,
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(wishText).then(() => {
            showNotification('Wish copied to clipboard!', 'success');
        });
    }
}

// Reset application
function resetApplication() {
    userData = {
        siblingName: '',
        yourName: '',
        answers: []
    };
    
    currentQuestion = 1;
    
    // Reset form
    loginForm.reset();
    
    // Reset questions
    document.getElementById('question-1').classList.add('active');
    document.getElementById('question-2').classList.remove('active');
    
    // Reset progress bar
    progressFill.style.width = '50%';
    
    // Clear selections
    document.querySelectorAll('.option-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
    
    // Hide next button
    nextQuestionBtn.classList.add('hidden');
    
    // Return to login section
    transitionToSection(loginSection);
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background: ${type === 'error' ? '#ff6b6b' : '#48dbfb'};
        color: white;
        border-radius: 8px;
        z-index: 1000;
        animation: slideInRight 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Create additional floating elements
function createFloatingElements() {
    const container = document.querySelector('.background-animation');
    const hearts = ['💖', '💝', '🎁', '🌺', '🌟', '✨'];
    
    setInterval(() => {
        const heart = document.createElement('div');
        heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.cssText = `
            position: absolute;
            font-size: 1.5rem;
            opacity: 0.6;
            pointer-events: none;
            animation: floatUp 4s linear forwards;
            left: ${Math.random() * 100}%;
            bottom: -50px;
        `;
        
        container.appendChild(heart);
        
        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 4000);
    }, 3000);
}

// Add CSS for additional animations
const additionalStyles = `
    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(-20px); }
    }
    
    @keyframes slideInRight {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); }
        to { transform: translateX(100%); }
    }
    
    @keyframes floatUp {
        from { 
            transform: translateY(0) rotate(0deg);
            opacity: 0.6;
        }
        to { 
            transform: translateY(-100vh) rotate(360deg);
            opacity: 0;
        }
    }
    
    .blessing {
        margin-top: 20px;
        padding: 15px;
        background: linear-gradient(135deg, rgba(255, 159, 243, 0.1), rgba(72, 219, 251, 0.1));
        border-radius: 10px;
        text-align: center;
        font-style: italic;
        color: #666;
    }
`;

// Inject additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
